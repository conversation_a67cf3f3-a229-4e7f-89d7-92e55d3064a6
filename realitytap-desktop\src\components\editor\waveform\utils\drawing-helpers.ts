/**
 * 绘制辅助函数
 * 专门处理网格线、坐标轴等绘制功能
 */

import { AXIS_COLOR, GRID_COLOR, AXIS_LABEL_COLOR, FONT_STYLE } from "./color";
import { logger, LogModule } from "@/utils/logger/logger";
import {
  DEFAULT_WAVEFORM_LINE_WIDTH,
  SELECTED_WAVEFORM_LINE_WIDTH,
  DEFAULT_WAVEFORM_POINT_RADIUS,
  SELECTED_WAVEFORM_POINT_RADIUS,
  MIN_FREQUENCY,
  MAX_FREQUENCY,
  WAVEFORM_SAFE_OFFSET,
  MIN_VISIBLE_TICK_COUNT,
  MAX_VISIBLE_TICK_COUNT,
  MIN_END_REMAINDER_RATIO,
  MAX_END_REMAINDER_RATIO,
  STEP_ADJUSTMENT_TOLERANCE,
  ENABLE_END_DISTANCE_OPTIMIZATION,
} from "../config/waveform-constants";

// 重新导出常量以保持向后兼容性
export const DEFAULT_LINE_WIDTH = DEFAULT_WAVEFORM_LINE_WIDTH;
export const SELECTED_LINE_WIDTH = SELECTED_WAVEFORM_LINE_WIDTH;
export const DEFAULT_POINT_RADIUS = DEFAULT_WAVEFORM_POINT_RADIUS;
export const SELECTED_POINT_RADIUS = SELECTED_WAVEFORM_POINT_RADIUS;

// 重新导出频率和偏移常量
export { MIN_FREQUENCY, MAX_FREQUENCY, WAVEFORM_SAFE_OFFSET };

// 其他绘制相关常量
export const ENABLE_GUIDE_LINES = true; // 辅助线功能开关

// 移除未使用的导入，直接使用数值

/**
 * 计算可视范围内的时间长度
 * 基于父元素可视宽度、总时长和缩放级别的精确计算
 */
function calculateVisibleTimeRange(
  parentVisibleWidth: number,
  totalDuration: number,
  zoomLevel: number,
  PADDING: { left: number; right: number },
  logicalCanvasWidth: number
): number {
  // 【修复】使用父元素的可视宽度，而不是Canvas逻辑宽度
  const availableVisibleWidth = parentVisibleWidth - PADDING.left - PADDING.right;

  // 计算逻辑绘图区域宽度（去除padding）
  const logicalDrawingWidth = logicalCanvasWidth - PADDING.left - PADDING.right;

  // 【关键修复】基于父元素可视范围计算可视时间
  //
  // 在缩放状态下的正确计算：
  // 1. 缩放后的逻辑宽度 = logicalDrawingWidth * zoomLevel
  // 2. 可视时间比例 = 父元素可视宽度 / 缩放后的逻辑宽度
  // 3. 可视时间 = 总时长 * 可视时间比例

  const scaledLogicalWidth = logicalDrawingWidth * zoomLevel;

  // 防止除零错误
  if (scaledLogicalWidth <= 0) {
    return totalDuration;
  }

  // 计算可视时间范围
  const visibleTimeRatio = availableVisibleWidth / scaledLogicalWidth;
  const visibleTimeRange = totalDuration * visibleTimeRatio;

  // 确保可视时间在合理范围内
  return Math.max(0, Math.min(visibleTimeRange, totalDuration));
}

/**
 * 检查终点刻度距离是否合适
 * @param effectiveDuration 总时长
 * @param timeStep 步进值
 * @returns 是否合适
 *
 * 性能优化：
 * - 使用简单的数学运算，避免复杂计算
 * - 直接返回布尔值，避免中间变量
 */
export function checkEndDistance(effectiveDuration: number, timeStep: number): boolean {
  const remainder = effectiveDuration % timeStep;
  const remainderRatio = remainder / timeStep;

  // 余数比例在合适范围内：不太小（避免过近）也不太大（避免浪费空间）
  return remainderRatio >= MIN_END_REMAINDER_RATIO &&
         remainderRatio <= MAX_END_REMAINDER_RATIO;
}

/**
 * 生成调整后的候选步进值
 * @param originalStep 原始步进值
 * @param effectiveDuration 总时长
 * @returns 调整后的候选步进值数组
 *
 * 性能优化：
 * - 限制候选步进值的数量（通常5-10个）
 * - 提前退出循环，找到第一个合适的值即可
 * - 避免创建过大的数组
 */
export function generateAdjustedSteps(originalStep: number, effectiveDuration: number): number[] {
  const adjustedSteps: number[] = [originalStep];
  const tolerance = STEP_ADJUSTMENT_TOLERANCE;

  // 在容差范围内生成候选步进值
  const minAdjustedStep = Math.max(1, Math.floor(originalStep * (1 - tolerance)));
  const maxAdjustedStep = Math.ceil(originalStep * (1 + tolerance));

  // 性能优化：限制搜索范围，避免过多候选值
  const maxCandidates = 5; // 最多5个候选值
  let candidateCount = 0;

  for (let step = minAdjustedStep; step <= maxAdjustedStep && candidateCount < maxCandidates; step++) {
    if (step !== originalStep && checkEndDistance(effectiveDuration, step)) {
      adjustedSteps.push(step);
      candidateCount++;
    }
  }

  return adjustedSteps;
}

/**
 * 新的智能步进计算算法
 * 考虑终点刻度距离，确保刻度分布均匀
 */
export function calculateOptimalTimeStepWithEndCheck(
  visibleTimeRange: number,
  availableWidth: number,
  effectiveDuration: number
): number {
  if (visibleTimeRange <= 0 || availableWidth <= 0 || effectiveDuration <= 0) return 1;

  // 如果禁用优化，回退到原始算法
  if (!ENABLE_END_DISTANCE_OPTIMIZATION) {
    logger.debug(LogModule.WAVEFORM, '[TimeStep] 终点距离优化已禁用，使用原始算法');
    return calculateOptimalTimeStep(visibleTimeRange, availableWidth);
  }

  logger.debug(LogModule.WAVEFORM, '[TimeStep] 开始智能步进计算', {
    visibleTimeRange,
    effectiveDuration,
    availableWidth
  });

  const maxTicks = MAX_VISIBLE_TICK_COUNT; // 15
  const minTicks = MIN_VISIBLE_TICK_COUNT; // 10

  // 基于可视时间范围和刻度数量限制计算步进值范围
  const minStep = Math.ceil(visibleTimeRange / maxTicks);
  const maxStep = Math.floor(visibleTimeRange / minTicks);

  // 确保步进值范围有效
  if (minStep > maxStep) {
    return Math.max(1, minStep);
  }

  // 生成候选步进值并进行终点距离检查
  const validSteps: number[] = [];

  for (let step = minStep; step <= maxStep; step++) {
    if (checkEndDistance(effectiveDuration, step)) {
      validSteps.push(step);
    }
  }

  // 如果有合适的步进值，选择最能整除可视时间范围的
  if (validSteps.length > 0) {
    let bestStep = validSteps[0];
    let minRemainder = visibleTimeRange % bestStep;

    for (const step of validSteps) {
      const remainder = visibleTimeRange % step;
      if (remainder < minRemainder) {
        minRemainder = remainder;
        bestStep = step;
      }
    }

    const endRemainder = effectiveDuration % bestStep;
    const endRemainderRatio = endRemainder / bestStep;

    logger.debug(LogModule.WAVEFORM, '[TimeStep] 找到合适的步进值', {
      bestStep,
      validStepsCount: validSteps.length,
      endRemainder,
      endRemainderRatio: endRemainderRatio.toFixed(3),
      visibleRemainder: minRemainder
    });

    return bestStep;
  }

  logger.debug(LogModule.WAVEFORM, '[TimeStep] 未找到合适的步进值，尝试调整');

  // 如果没有合适的步进值，尝试调整步进值
  for (let step = minStep; step <= maxStep; step++) {
    const adjustedSteps = generateAdjustedSteps(step, effectiveDuration);

    if (adjustedSteps.length > 1) { // 有调整后的候选值
      // 选择最能整除可视时间范围的调整后步进值
      let bestAdjustedStep = adjustedSteps[0];
      let minRemainder = visibleTimeRange % bestAdjustedStep;

      for (const adjustedStep of adjustedSteps) {
        const remainder = visibleTimeRange % adjustedStep;
        if (remainder < minRemainder) {
          minRemainder = remainder;
          bestAdjustedStep = adjustedStep;
        }
      }

      return bestAdjustedStep;
    }
  }

  // 最终回退到原始算法
  logger.debug(LogModule.WAVEFORM, '[TimeStep] 调整失败，回退到原始算法');
  const fallbackStep = calculateOptimalTimeStep(visibleTimeRange, availableWidth);

  logger.debug(LogModule.WAVEFORM, '[TimeStep] 最终步进值', {
    fallbackStep,
    endRemainder: effectiveDuration % fallbackStep,
    endRemainderRatio: ((effectiveDuration % fallbackStep) / fallbackStep).toFixed(3)
  });

  return fallbackStep;
}

/**
 * 基于可视时间范围计算合适的刻度步进值
 * 确保可视范围内的刻度数量严格在指定范围内
 */
function calculateOptimalTimeStep(visibleTimeRange: number, availableWidth: number): number {
  if (visibleTimeRange <= 0 || availableWidth <= 0) return 1;

  // 【修复】直接基于常量限制计算刻度数量范围
  // 不再考虑像素密度，优先保证刻度数量在限制范围内
  const maxTicks = MAX_VISIBLE_TICK_COUNT; // 15
  const minTicks = MIN_VISIBLE_TICK_COUNT; // 10

  // 基于可视时间范围和刻度数量限制计算步进值范围
  const minStep = Math.ceil(visibleTimeRange / maxTicks); // 最小步进值（最多刻度数）
  const maxStep = Math.floor(visibleTimeRange / minTicks); // 最大步进值（最少刻度数）

  // 确保步进值范围有效
  if (minStep > maxStep) {
    // 如果范围无效，使用最小步进值
    return Math.max(1, minStep);
  }

  // 生成候选步进值
  const candidateSteps: number[] = [];
  for (let step = minStep; step <= maxStep; step++) {
    candidateSteps.push(step);
  }

  if (candidateSteps.length === 0) {
    candidateSteps.push(Math.max(1, minStep));
  }

  // 选择最能整除可视时间范围的步进值
  let bestStep = candidateSteps[0];
  let minRemainder = visibleTimeRange % bestStep;

  for (const step of candidateSteps) {
    const remainder = visibleTimeRange % step;
    if (remainder < minRemainder) {
      minRemainder = remainder;
      bestStep = step;
    }
  }

  // 【验证】确保计算出的步进值能产生合理的刻度数量
  const resultingTickCount = Math.ceil(visibleTimeRange / bestStep);

  // 如果刻度数量仍然超出范围，强制调整步进值
  if (resultingTickCount > MAX_VISIBLE_TICK_COUNT) {
    bestStep = Math.ceil(visibleTimeRange / MAX_VISIBLE_TICK_COUNT);
  } else if (resultingTickCount < MIN_VISIBLE_TICK_COUNT) {
    bestStep = Math.floor(visibleTimeRange / MIN_VISIBLE_TICK_COUNT);
  }

  return Math.max(1, bestStep);
}

/**
 * 绘制X轴刻度和标签
 * 新的智能刻度计算算法：
 * 1. 基于可视范围内的时间长度计算
 * 2. 确保可视范围内的刻度数量在指定范围内
 * 3. 缩放时保持可视范围内合理的刻度密度
 */
export function drawXAxisTicksAndLabels(
  ctx: CanvasRenderingContext2D,
  graphHeight: number,
  effectiveDuration: number,
  mapTimeToXLocal: (time: number) => number,
  PADDING: { top: number; right: number; bottom: number; left: number },
  zoomLevel: number = 1.0,
  parentVisibleWidth?: number,
  logicalCanvasWidth?: number
) {
  if (effectiveDuration <= 0) return;

  // 计算画布宽度和容差
  const canvasWidth = ctx.canvas.width / (window.devicePixelRatio || 1);
  const tolerance = 50;
  const availableWidth = canvasWidth - PADDING.left - PADDING.right;

  // 1. 计算可视范围内的时间长度
  // 检查父元素宽度是否有效（大于0）
  let visibleTimeRange: number;
  if (parentVisibleWidth && parentVisibleWidth > 0 && logicalCanvasWidth && logicalCanvasWidth > 0) {
    visibleTimeRange = calculateVisibleTimeRange(parentVisibleWidth, effectiveDuration, zoomLevel, PADDING, logicalCanvasWidth);
  } else {
    // 【回退策略】当父元素宽度无效时，使用Canvas可用宽度作为替代
    const fallbackParentWidth = availableWidth + PADDING.left + PADDING.right;
    if (logicalCanvasWidth && logicalCanvasWidth > 0) {
      visibleTimeRange = calculateVisibleTimeRange(fallbackParentWidth, effectiveDuration, zoomLevel, PADDING, logicalCanvasWidth);
    } else {
      // 最终回退：简化计算
      visibleTimeRange = effectiveDuration / zoomLevel;
      visibleTimeRange = Math.max(0, Math.min(visibleTimeRange, effectiveDuration));
    }
  }

  // 2. 基于可视时间范围计算最优步进值，使用新的智能算法考虑终点距离
  const finalTimeStep = calculateOptimalTimeStepWithEndCheck(visibleTimeRange, availableWidth, effectiveDuration);

  // 计算刻度分布信息用于调试
  const lastStepTime = Math.floor(effectiveDuration / finalTimeStep) * finalTimeStep;
  const endRemainder = effectiveDuration - lastStepTime;
  const endRemainderRatio = endRemainder / finalTimeStep;
  const tickCount = Math.ceil(visibleTimeRange / finalTimeStep);

  logger.debug(LogModule.WAVEFORM, '[TimeStep] 刻度分布信息', {
    finalTimeStep,
    effectiveDuration,
    lastStepTime,
    endRemainder: endRemainder.toFixed(1),
    endRemainderRatio: endRemainderRatio.toFixed(3),
    tickCount,
    visibleTimeRange: visibleTimeRange.toFixed(1)
  });

  // 3. 计算实际的刻度数量
  // 时间标签格式化函数 - 始终显示毫秒格式
  // 根据触觉反馈系统精确时间控制要求，禁止时间单位自动转换
  const formatTimeLabel = (timeMs: number, showUnit: boolean = true): string => {
    // 始终显示毫秒格式，不进行任何时间单位转换
    const value = Math.round(timeMs).toString();
    return showUnit ? `${value}ms` : value;
  };

  // 绘制刻度和标签
  const startTick = Math.ceil(0 / finalTimeStep) * finalTimeStep;

  ctx.textAlign = "center";
  ctx.textBaseline = "top";

  // 绘制内部刻度（不包括最后一个刻度）
  // lastStepTime 已在上面计算过，直接使用
  for (let time = startTick; time < lastStepTime; time += finalTimeStep) {
    const x = mapTimeToXLocal(time);

    // 绘制刻度线
    ctx.beginPath();
    ctx.moveTo(x, PADDING.top + graphHeight);
    ctx.lineTo(x, PADDING.top + graphHeight + 4);
    ctx.strokeStyle = AXIS_COLOR;
    ctx.stroke();

    // 使用智能格式化的标签，内部刻度不显示单位
    const label = time === 0 ? "0" : formatTimeLabel(time, false);
    ctx.fillText(label, x - 2, PADDING.top + graphHeight + 8);

    // 0刻度不绘制垂直网格线，其它刻度正常绘制
    if (time !== 0) {
      ctx.beginPath();
      ctx.moveTo(x, PADDING.top);
      ctx.lineTo(x, PADDING.top + graphHeight);
      ctx.strokeStyle = GRID_COLOR;
      ctx.stroke();
    }
  }

  // 绘制最后一个步进刻度（可能是整除的lastStepTime或effectiveDuration本身）
  if (Math.abs(effectiveDuration - lastStepTime) <= 0.001) {
    // 整除情况：绘制lastStepTime处的刻度
    const x = mapTimeToXLocal(lastStepTime);

    // 检查刻度是否在可见范围内
    if (x >= -tolerance && x <= canvasWidth + tolerance) {
      // 绘制刻度线
      ctx.beginPath();
      ctx.moveTo(x, PADDING.top + graphHeight);
      ctx.lineTo(x, PADDING.top + graphHeight + 4);
      ctx.strokeStyle = AXIS_COLOR;
      ctx.stroke();

      // 使用智能格式化的标签，最后一个刻度显示单位
      const label = formatTimeLabel(lastStepTime, true);
      ctx.fillText(label, x - 2, PADDING.top + graphHeight + 8);

      // 最后一个垂直网格线
      ctx.beginPath();
      ctx.moveTo(x, PADDING.top);
      ctx.lineTo(x, PADDING.top + graphHeight);
      ctx.strokeStyle = GRID_COLOR;
      ctx.stroke();
    }
  } else {
    // 非整除情况：在lastStepTime处绘制常规刻度
    const xLast = mapTimeToXLocal(lastStepTime);

    // 检查常规刻度是否在可见范围内
    if (xLast >= -tolerance && xLast <= canvasWidth + tolerance) {
      // 绘制刻度线
      ctx.beginPath();
      ctx.moveTo(xLast, PADDING.top + graphHeight);
      ctx.lineTo(xLast, PADDING.top + graphHeight + 4);
      ctx.strokeStyle = AXIS_COLOR;
      ctx.stroke();

      // 使用智能格式化的标签，不显示单位（因为不是最后一个）
      const label = formatTimeLabel(lastStepTime, false);
      ctx.fillText(label, xLast - 2, PADDING.top + graphHeight + 8);

      // 垂直网格线
      ctx.beginPath();
      ctx.moveTo(xLast, PADDING.top);
      ctx.lineTo(xLast, PADDING.top + graphHeight);
      ctx.strokeStyle = GRID_COLOR;
      ctx.stroke();
    }

    // 额外绘制终点刻度
    const xEnd = mapTimeToXLocal(effectiveDuration);

    // 检查终点刻度是否在可见范围内，如果在则绘制
    // 使用更宽松的可见性检查，确保边界刻度能够显示
    // 特别处理最小缩放率情况，确保终点刻度始终显示
    const isEndVisible = xEnd >= -tolerance && xEnd <= canvasWidth + tolerance;
    const shouldForceShowEnd = zoomLevel <= 0.5; // 在低缩放率时强制显示终点刻度

    if (isEndVisible || shouldForceShowEnd) {
      // 绘制终点刻度线
      ctx.beginPath();
      ctx.moveTo(xEnd, PADDING.top + graphHeight);
      ctx.lineTo(xEnd, PADDING.top + graphHeight + 4);
      ctx.strokeStyle = AXIS_COLOR;
      ctx.stroke();

      // 使用智能格式化的终点标签，最后一个刻度显示单位
      const endLabel = formatTimeLabel(effectiveDuration, true);
      ctx.fillText(endLabel, xEnd - 2, PADDING.top + graphHeight + 8);

      // 终点垂直网格线
      ctx.beginPath();
      ctx.moveTo(xEnd, PADDING.top);
      ctx.lineTo(xEnd, PADDING.top + graphHeight);
      ctx.strokeStyle = GRID_COLOR;
      ctx.stroke();
    }
  }
}

/**
 * 绘制网格线和X轴
 */
export function drawGridLinesAndXAxis(
  ctx: CanvasRenderingContext2D,
  graphWidth: number,
  graphHeight: number,
  mapIntensityToYLocal: (intensity: number) => number,
  PADDING: { top: number; right: number; bottom: number; left: number },
  WAVEFORM_SAFE_OFFSET: number,
  effectiveDuration: number,
  mapTimeToXLocal: (time: number) => number,
  zoomLevel: number = 1.0, // 新增缩放级别参数
  parentVisibleWidth?: number, // 新增父元素可视宽度参数
  logicalCanvasWidth?: number // 新增逻辑Canvas宽度参数
) {
  ctx.lineWidth = 1;
  ctx.font = FONT_STYLE;
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";

  // 计算实际时长对应的X坐标，限制绘制范围
  const effectiveDurationX = mapTimeToXLocal(effectiveDuration);
  const maxDrawX = Math.min(graphWidth + WAVEFORM_SAFE_OFFSET, effectiveDurationX);

  // 水平网格线 - 限制在实际时长范围内
  for (let intensity = 20; intensity <= 100; intensity += 20) {
    const y = mapIntensityToYLocal(intensity);
    ctx.beginPath();
    ctx.moveTo(0, y);
    ctx.lineTo(maxDrawX, y);
    ctx.strokeStyle = GRID_COLOR;
    ctx.stroke();
  }

  // X-Axis (Time) - 限制在实际时长范围内
  ctx.beginPath();
  ctx.strokeStyle = AXIS_COLOR;
  ctx.fillStyle = AXIS_LABEL_COLOR;
  ctx.moveTo(0, PADDING.top + graphHeight);
  ctx.lineTo(maxDrawX, PADDING.top + graphHeight);
  ctx.stroke();

  // 绘制X轴刻度和标签，传递所有必要参数
  drawXAxisTicksAndLabels(ctx, graphHeight, effectiveDuration, mapTimeToXLocal, PADDING, zoomLevel, parentVisibleWidth, logicalCanvasWidth);
}
